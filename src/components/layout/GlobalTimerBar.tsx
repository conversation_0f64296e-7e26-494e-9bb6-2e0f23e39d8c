import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Autocomplete,
  TextField,
  Chip,
  IconButton,
  // Tooltip, // Commented out unused import
  Stack,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Timer as TimerIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  History as HistoryIcon,
} from '@mui/icons-material';
import { TimeEntry } from '../../types/timer';
import { Task } from '../../types/task';
import { TimerDisplay } from '../ui/display/TimerDisplay';
import { useTimer } from '../../hooks/useTimer';
import { useFavoriteTasks } from '../../hooks/useFavoriteTasks';

interface GlobalTimerBarProps {
  activeEntry: TimeEntry | null;
  predefinedTasks: Task[];
  timeEntries: TimeEntry[];
  onStart: (taskName: string) => void;
  onStop: () => void;
}

export function GlobalTimerBar({
  activeEntry,
  predefinedTasks,
  timeEntries,
  onStart,
  onStop,
}: GlobalTimerBarProps) {
  const [selectedTask, setSelectedTask] = useState<string>('');
  const [inputValue, setInputValue] = useState<string>('');

  // Calculate elapsed time for running timer
  const elapsed = useTimer(
    activeEntry?.isRunning || false,
    activeEntry?.startTime
  );

  // Favorite and recent tasks functionality
  const {
    quickAccessTasks,
    // favoriteTasks, // Commented out unused variable
    // recentTasks, // Commented out unused variable
    toggleFavorite,
    isFavorite,
  } = useFavoriteTasks(predefinedTasks, timeEntries);

  const taskOptions = predefinedTasks.map(task => task.name);

  const handleStart = () => {
    // Use selectedTask if available, otherwise fall back to inputValue
    const taskName = selectedTask.trim() || inputValue.trim();
    if (taskName) {
      onStart(taskName);
      setSelectedTask('');
      setInputValue('');
    }
  };

  const handleStop = () => {
    onStop();
  };

  const handleTaskChange = (_event: any, newValue: string | null) => {
    setSelectedTask(newValue || '');
  };

  const handleInputChange = (_event: any, newInputValue: string) => {
    setInputValue(newInputValue);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    const taskName = selectedTask.trim() || inputValue.trim();
    if (event.key === 'Enter' && taskName && !activeEntry) {
      handleStart();
    }
  };

  const handleQuickStart = (taskName: string) => {
    onStart(taskName);
  };

  const handleToggleFavorite = (taskId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    toggleFavorite(taskId);
  };

  // Timer stopped state
  if (!activeEntry || !activeEntry.isRunning) {
    return (
      <Paper
        elevation={1}
        sx={{
          p: 2,
          mb: 2,
          backgroundColor: 'background.paper',
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Stack spacing={2}>
          {/* Quick Access Tasks */}
          {quickAccessTasks.length > 0 && (
            <Box>
              <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                Quick Start:
              </Typography>
              <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
                {quickAccessTasks.slice(0, 6).map((task) => (
                  <Chip
                    key={task.id}
                    label={task.name}
                    size="small"
                    variant="outlined"
                    clickable
                    onClick={() => handleQuickStart(task.name)}
                    icon={
                      isFavorite(task.id) ? (
                        <StarIcon sx={{ fontSize: '16px !important' }} />
                      ) : (
                        <HistoryIcon sx={{ fontSize: '16px !important' }} />
                      )
                    }
                    sx={{
                      '&:hover': {
                        backgroundColor: 'primary.light',
                        color: 'primary.contrastText',
                      },
                    }}
                  />
                ))}
              </Stack>
            </Box>
          )}

          {/* Main Timer Controls */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <TimerIcon color="action" />

            <Typography variant="body2" color="text.secondary" sx={{ minWidth: 'fit-content' }}>
              Start Timer:
            </Typography>

            <Autocomplete
              value={selectedTask}
              onChange={handleTaskChange}
              inputValue={inputValue}
              onInputChange={handleInputChange}
              options={taskOptions}
              freeSolo
              disabled={activeEntry?.isRunning}
              sx={{ flex: 1, minWidth: 200 }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Select or type task name..."
                  size="small"
                  onKeyPress={handleKeyPress}
                  data-testid="global-timer-task-input"
                  aria-label="Task name input"
                  label="Task"
                />
              )}
              renderOption={(props, option) => {
                const task = predefinedTasks.find(t => t.name === option);
                return (
                  <Box component="li" {...props} key={option} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box sx={{ flex: 1 }}>{option}</Box>
                    {task && (
                      <IconButton
                        size="small"
                        onClick={(e) => handleToggleFavorite(task.id, e)}
                        sx={{ p: 0.5 }}
                      >
                        {isFavorite(task.id) ? (
                          <StarIcon sx={{ fontSize: 16, color: 'warning.main' }} />
                        ) : (
                          <StarBorderIcon sx={{ fontSize: 16 }} />
                        )}
                      </IconButton>
                    )}
                  </Box>
                );
              }}
            />

            <Button
              variant="contained"
              startIcon={<PlayIcon />}
              onClick={handleStart}
              disabled={!selectedTask.trim() && !inputValue.trim()}
              data-testid="global-timer-start-button"
            >
              Start
            </Button>
          </Box>
        </Stack>
      </Paper>
    );
  }

  // Timer running state
  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        mb: 2,
        backgroundColor: 'success.dark',
        borderRadius: 2,
        border: '1px solid',
        borderColor: 'success.main',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <TimerIcon sx={{ color: 'success.contrastText' }} />
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
          <Typography variant="body2" sx={{ color: 'success.contrastText' }}>
            Working on:
          </Typography>
          <Chip
            label={activeEntry.taskName}
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              color: 'success.contrastText',
              border: '1px solid rgba(255, 255, 255, 0.3)',
            }}
            size="small"
          />
        </Box>
        
        <TimerDisplay
          elapsed={elapsed}
          isRunning={activeEntry.isRunning}
          showTaskName={false}
          size="small"
          sx={{ 
            fontWeight: 600, 
            fontSize: '1.1rem',
            color: 'success.contrastText'
          }}
        />
        
        <Button
          variant="contained"
          color="error"
          startIcon={<StopIcon />}
          onClick={handleStop}
          data-testid="global-timer-stop-button"
        >
          Stop
        </Button>
      </Box>
    </Paper>
  );
}
